<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Visual Editor Test - Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js" defer=""></script>
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747200" defer=""></script>
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
        }
        
        .test-image {
            max-width: 300px;
            height: auto;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .test-link:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <header style="background: #0057B7; color: white; padding: 1rem; text-align: center;">
        <h1 data-ve-block-id="e094122a-d48b-443d-b258-7782e3e33cdb">Visual Editor Test Page</h1>
        <p data-ve-block-id="77573e42-c823-4f8f-b9d4-acbd5d6c51ee">This page is for testing the live visual editor functionality</p>
    </header>

    <div class="test-container">
        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="72bc23f9-005d-4d69-a45b-85a194113d0d">Text Editing Test</h2>
            <p data-ve-block-id="68a4cd4a-14f1-438f-bed5-2a59156018e1">This is a sample paragraph that can be edited. Click the edit button when in edit mode to modify this text.</p>
            <p data-ve-block-id="5a9f5d0e-4361-47c3-a16d-c69c693a2fcd">Here's another paragraph with <strong>bold text</strong> and <em>italic text</em> that you can edit.</p>
        </div>

        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="ee60e414-586d-44c8-94f7-89f644f3818a">Heading Editing Test</h2>
            <h3 data-ve-block-id="5cfa82cc-6a46-478b-b06b-7510866713b2">This is an editable heading</h3>
            <h4 data-ve-block-id="f180cebf-0f61-4047-8b33-708abc8160d1">This is another editable heading</h4>
        </div>

        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="b8f4c902-25a0-4895-a538-2998ff9e2cd1">Image Editing Test</h2>
            <img src="/images/centralShield.png" alt="Test Image" class="test-image" data-ve-block-id="b85a5fd2-ae85-4e94-9b27-a2f6807a6a39">
            <p data-ve-block-id="a68e6c1b-b935-498f-8b2f-7d7c5edeadaa">The image above can be replaced with a new URL when in edit mode.</p>
        </div>

        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="c7af3cd9-d031-4d3b-9068-c4a398b994ba">Link Editing Test</h2>
            <a href="https://example.com" class="test-link" data-ve-block-id="80147f29-a003-462a-a510-0acc969aba1c">This is an editable link</a>
            <p data-ve-block-id="cccd7eb4-9f42-46e2-9735-1b5d4d62efad">The link above can have its URL and text modified.</p>
        </div>

        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="4e243c38-635d-4cac-8796-5f2699c1b9ca">HTML Content Test</h2>
            <div class="html-content">
                <p data-ve-block-id="f2d5b98c-6c8d-47c5-a64f-9bf22cfbddfb">This section contains <strong>HTML content</strong> that can be edited:</p>
                <ul>
                    <li>List item one</li>
                    <li>List item two</li>
                    <li>List item three</li>
                </ul>
                <p data-ve-block-id="f5ba58e7-39c9-4926-8e64-8b58898f01d6">You can modify the HTML structure when editing this content.</p>
            </div>
        </div>

        <div class="test-section fade-in-section">
            <h2 data-ve-block-id="e9892f89-ef06-4264-963a-cfb54691e427">Instructions</h2>
            <div class="instructions">
                <h3 data-ve-block-id="bb230cbe-d290-4bb4-a155-e802aca9b914">How to use the Visual Editor:</h3>
                <ol>
                    <li><strong>Login as Admin:</strong> Go to <a href="/login.html" data-ve-block-id="538603e6-6db7-4cd4-9b72-32c6713c1d7b">login page</a> and login with admin credentials</li>
                    <li><strong>Enable Edit Mode:</strong> Click the "Edit Mode" button in the top-right corner or press Ctrl+E</li>
                    <li><strong>Edit Content:</strong> Hover over any element to see edit controls, then click "Edit"</li>
                    <li><strong>Save Changes:</strong> Use the modal to modify content and save changes</li>
                    <li><strong>Preview:</strong> Use the preview button to see changes temporarily</li>
                    <li><strong>Restore:</strong> Use the restore button to revert to original content</li>
                </ol>
                
                <h3 data-ve-block-id="11a280de-196b-41ea-8692-5742d65ca114">Supported Content Types:</h3>
                <ul>
                    <li><strong>Text:</strong> Simple text content (headings, paragraphs)</li>
                    <li><strong>HTML:</strong> Rich HTML content with formatting</li>
                    <li><strong>Images:</strong> Image URLs and alt text</li>
                    <li><strong>Links:</strong> Link URLs and display text</li>
                </ul>
            </div>
        </div>
    </div>

    <footer style="background: #f8f9fa; padding: 2rem; text-align: center; margin-top: 3rem;">
        <p data-ve-block-id="b3ae60d6-70d9-4e1c-abd0-851c8c94ad30">© 2024 Tutors Alliance Scotland - Visual Editor Test</p>
        <p data-ve-block-id="2db2292d-e1d1-4223-83f5-5492c6d5b4cb"><a href="/" data-ve-block-id="43ddf141-f2b6-4213-9e42-5871ea29db58">Back to Home</a> | <a href="/admin.html" data-ve-block-id="e7f1363e-33cf-424a-8836-a4068ee89d47">Admin Panel</a></p>
    </footer>
<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" class="form-control" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>
                        
                        <!-- Button Management for Text Elements -->
                        <div class="text-button-management">
                            <h4>Add Button</h4>
                            <p class="help-text">Add an aurora-style button at the end of this text element</p>
                            
                            <div id="text-buttons-list" class="text-buttons-list">
                                <!-- Existing buttons will be listed here -->
                            </div>
                            
                            <div class="button-form" id="new-button-form" style="display: none;">
                                <div class="form-group">
                                    <label for="new-button-text">Button Text:</label>
                                    <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                </div>
                                <div class="form-group">
                                    <label for="new-button-url">Button URL:</label>
                                    <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                </div>
                                <div class="button-form-actions">
                                    <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                    <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                            
                            <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                        </div>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*" class="form-control">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                        <div class="form-check">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>


</body></html>